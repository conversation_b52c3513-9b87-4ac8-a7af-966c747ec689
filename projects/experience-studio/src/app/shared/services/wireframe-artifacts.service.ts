import { Injectable, inject, DestroyRef, computed, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, interval, Subscription } from 'rxjs';
import { DesignToken, ApplicationLog, WireframeArtifactState } from '../interfaces/wireframe-artifacts.interface';

// ENHANCED: Comprehensive design tokens matching existing code-generation format
const MOCK_DESIGN_TOKENS: DesignToken[] = [
  // Color tokens matching existing design system format
  {
    id: 'color-0',
    name: 'Primary Blue',
    value: '#3b82f6',
    type: 'color',
    category: 'Colors',
    editable: true
  },
  {
    id: 'color-1',
    name: 'Secondary Purple',
    value: '#8b5cf6',
    type: 'color',
    category: 'Colors',
    editable: true
  },
  {
    id: 'color-2',
    name: 'Success Green',
    value: '#10b981',
    type: 'color',
    category: 'Colors',
    editable: true
  },
  {
    id: 'color-3',
    name: 'Warning Orange',
    value: '#f59e0b',
    type: 'color',
    category: 'Colors',
    editable: true
  },
  {
    id: 'color-4',
    name: 'Error Red',
    value: '#ef4444',
    type: 'color',
    category: 'Colors',
    editable: true
  },
  {
    id: 'color-5',
    name: 'Neutral Gray',
    value: '#6b7280',
    type: 'color',
    category: 'Colors',
    editable: true
  },
  // Typography tokens
  {
    id: 'typography-0',
    name: 'Headline 1',
    value: 'Semi Bold, 32/40',
    type: 'typography',
    category: 'Font Style Desktop',
    editable: true
  },
  {
    id: 'typography-1',
    name: 'Headline 2',
    value: 'Semi Bold, 24/32',
    type: 'typography',
    category: 'Font Style Desktop',
    editable: true
  },
  {
    id: 'typography-2',
    name: 'Body Text',
    value: 'Regular, 16/24',
    type: 'typography',
    category: 'Font Style Desktop',
    editable: true
  }
];

// ENHANCED: Realistic wireframe generation log messages with detailed progress tracking
const MOCK_LOG_MESSAGES = [
  {
    phase: 'initialization',
    message: 'Starting wireframe generation engine...',
    level: 'info' as const,
    progress: 5
  },
  {
    phase: 'analysis',
    message: 'Analyzing user requirements and design constraints...',
    level: 'info' as const,
    progress: 10
  },
  {
    phase: 'parsing',
    message: 'Parsing uploaded documents and extracting key information...',
    level: 'info' as const,
    progress: 15
  },
  {
    phase: 'layout-detection',
    message: 'Detecting optimal layout patterns from requirements...',
    level: 'info' as const,
    progress: 25
  },
  {
    phase: 'grid-system',
    message: 'Generating responsive grid system and breakpoints...',
    level: 'info' as const,
    progress: 35
  },
  {
    phase: 'component-mapping',
    message: 'Mapping UI components to wireframe elements...',
    level: 'info' as const,
    progress: 45
  },
  {
    phase: 'design-tokens',
    message: 'Applying design system tokens and color palette...',
    level: 'info' as const,
    progress: 55
  },
  {
    phase: 'hierarchy',
    message: 'Building component hierarchy and relationships...',
    level: 'info' as const,
    progress: 65
  },
  {
    phase: 'responsive-optimization',
    message: 'Optimizing wireframe for mobile and desktop views...',
    level: 'info' as const,
    progress: 75
  },
  {
    phase: 'accessibility-validation',
    message: 'Validating accessibility standards and WCAG compliance...',
    level: 'info' as const,
    progress: 85
  },
  {
    phase: 'finalization',
    message: 'Finalizing wireframe structure and generating artifacts...',
    level: 'info' as const,
    progress: 95
  },
  {
    phase: 'completion',
    message: 'Wireframe generation completed successfully! Ready for preview.',
    level: 'info' as const,
    progress: 100
  }
];

@Injectable({
  providedIn: 'root'
})
export class WireframeArtifactsService {
  private readonly destroyRef = inject(DestroyRef);

  private currentLogIndex = 0;
  private logInterval$ = new BehaviorSubject<number>(5000); // 5 seconds default
  private logUpdateSubscription?: Subscription;
  private isWireframeMode = signal<boolean>(false);

  // State management using signals
  private readonly state = signal<WireframeArtifactState>({
    designTokens: [],
    applicationLogs: [],
    isLoading: false,
    error: null
  });

  // Exposed computed signals for components
  readonly designTokens = computed(() => this.state().designTokens);
  readonly applicationLogs = computed(() => this.state().applicationLogs);
  readonly isLoading = computed(() => this.state().isLoading);
  readonly error = computed(() => this.state().error);

  // Expose isWireframeMode as a computed signal
  readonly isWireframeGenerationMode = computed(() => this.isWireframeMode());

  constructor() {
    // No automatic initialization - wait for explicit start
  }

  private initializeMockDataStream(): void {
    // Set initial design tokens
    this.updateState({ designTokens: MOCK_DESIGN_TOKENS });

    // Cleanup any existing subscription
    this.cleanupLogSubscription();

    // Start new log updates
    this.logUpdateSubscription = interval(this.logInterval$.value)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        if (this.isWireframeMode()) {
          this.addMockLog();
        }
      });
  }

  private cleanupLogSubscription(): void {
    if (this.logUpdateSubscription) {
      this.logUpdateSubscription.unsubscribe();
      this.logUpdateSubscription = undefined;
    }
  }

  private addMockLog(): void {
    const mockLogData = MOCK_LOG_MESSAGES[this.currentLogIndex % MOCK_LOG_MESSAGES.length];

    const newLog: ApplicationLog = {
      timestamp: new Date().toISOString(),
      level: mockLogData.level,
      message: mockLogData.message,
      details: `Phase: ${mockLogData.phase}, Progress: ${mockLogData.progress}%`
    };

    this.updateState({
      applicationLogs: [...this.state().applicationLogs, newLog]
    });

    this.currentLogIndex++;
  }

  private updateState(partialState: Partial<WireframeArtifactState>): void {
    this.state.update(currentState => ({
      ...currentState,
      ...partialState
    }));
  }

  startArtifactsGeneration(): void {
    this.isWireframeMode.set(true);
    this.updateState({
      isLoading: true,
      error: null,
      applicationLogs: [],
      designTokens: MOCK_DESIGN_TOKENS
    });
    this.currentLogIndex = 0;
    this.initializeMockDataStream();
  }

  stopArtifactsGeneration(): void {
    this.isWireframeMode.set(false);
    this.updateState({ isLoading: false });
    this.cleanupLogSubscription();
  }

  setError(error: string): void {
    this.updateState({
      error,
      isLoading: false
    });
    // Don't stop the wireframe mode or cleanup - let the caller decide
  }

  reset(): void {
    this.isWireframeMode.set(false);
    this.updateState({
      designTokens: [],
      applicationLogs: [],
      isLoading: false,
      error: null
    });
    this.currentLogIndex = 0;
    this.cleanupLogSubscription();
  }

  setLogInterval(interval: number): void {
    this.logInterval$.next(interval);
    // Restart the log stream if we're in wireframe mode
    if (this.isWireframeMode()) {
      this.initializeMockDataStream();
    }
  }
}
